const fastify = require('fastify')({
  logger: true
});

// 第一个测试接口：GET /api/hello
// 返回简单的问候消息
fastify.get('/api/hello', async (request, reply) => {
  const { name = 'World' } = request.query;

  return {
    success: true,
    message: `Hello, ${name}!`,
    timestamp: new Date().toISOString(),
    endpoint: 'GET /api/hello'
  };
});

// 第二个测试接口：POST /api/user
// 接收用户数据并返回处理结果
fastify.post('/api/user', async (request, reply) => {
  const { name, email, age } = request.body || {};

  // 简单的数据验证
  if (!name || !email) {
    reply.code(400);
    return {
      success: false,
      error: 'Name and email are required',
      timestamp: new Date().toISOString()
    };
  }

  // 模拟用户创建
  const user = {
    id: Math.floor(Math.random() * 10000),
    name,
    email,
    age: age || null,
    createdAt: new Date().toISOString()
  };

  reply.code(201);
  return {
    success: true,
    message: 'User created successfully',
    data: user,
    endpoint: 'POST /api/user'
  };
});

// 健康检查接口
fastify.get('/health', async (request, reply) => {
  return {
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  };
});

// 启动服务器
const start = async () => {
  try {
    const port = process.env.PORT || 3000;
    const host = process.env.HOST || '0.0.0.0';

    await fastify.listen({ port, host });
    console.log(`🚀 Server is running on http://${host}:${port}`);
    console.log('📋 Available endpoints:');
    console.log('  GET  /api/hello?name=YourName');
    console.log('  POST /api/user');
    console.log('  GET  /health');
  } catch (err) {
    fastify.log.error(err);
    process.exit(1);
  }
};

start();