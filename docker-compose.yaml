services:
  fluentd:
    image: fluent/fluentd:v1.3
    container_name: fluentd
    restart: unless-stopped
    ports:
      - "24224:24224"
    volumes:
      - ./fluent.conf:/fluentd/etc/fluent.conf
      - fluentd_log:/fluentd/log
    # tty: true
    # stdin_open: true
    # 如果需要后台运行/守护态，其实 compose 默认就是后台，docker compose up -d 即可
  nginx:
    image: nginx
    container_name: nginx
    ports:
      - "8080:80"
    depends_on:
      - fluentd
    # volumes:
    #   - ./nginx.conf:/etc/nginx/nginx.conf
    logging:
      driver: fluentd
      options:
        fluentd-address: "host.docker.internal:24224"
        mode: "non-blocking"
        tag: "docker.nginx"
volumes:
  fluentd_log:   # 